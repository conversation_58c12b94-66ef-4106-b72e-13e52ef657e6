import { useEffect } from 'react';
import { register, unregister } from '@tauri-apps/plugin-global-shortcut';
import { invoke } from '@tauri-apps/api/core';

export const useGlobalShortcuts = () => {
  useEffect(() => {
    const registerShortcuts = async () => {
      try {
        console.log('⌨️ [SHORTCUT] Registering global shortcuts...');

        // Register Ctrl+X for clearing all user data and reloading
        await register('Ctrl+X', async (event) => {
          if (event.state === 'Pressed') {
            console.log('⌨️ [SHORTCUT] Ctrl+X pressed - clearing all user data and reloading...');
            try {
              // Clear all user data
              await invoke('clear_all_user_data');
              console.log('✅ [SHORTCUT] User data cleared, reloading app...');
              
              // Reload the app
              await invoke('reload_window');
            } catch (error) {
              console.error('❌ [SHORTCUT] Failed to clear data and reload:', error);
            }
          }
        });

        // Register Ctrl+R for reloading windows
        await register('Ctrl+R', async (event) => {
          if (event.state === 'Pressed') {
            console.log('⌨️ [SHORTCUT] Ctrl+R pressed - reloading windows...');
            try {
              await invoke('reload_window');
            } catch (error) {
              console.error('❌ [SHORTCUT] Failed to reload window:', error);
            }
          }
        });

        console.log('✅ [SHORTCUT] Global shortcuts registered successfully');
      } catch (error) {
        console.error('❌ [SHORTCUT] Failed to register shortcuts:', error);
      }
    };

    registerShortcuts();

    // Cleanup function to unregister shortcuts
    return () => {
      const cleanup = async () => {
        try {
          await unregister('Ctrl+X');
          await unregister('Ctrl+R');
          console.log('🧹 [SHORTCUT] Global shortcuts unregistered');
        } catch (error) {
          console.error('❌ [SHORTCUT] Failed to unregister shortcuts:', error);
        }
      };
      cleanup();
    };
  }, []);
};