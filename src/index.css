/* Google Fonts removed for Tauri compatibility */

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Shimmer Animation for Skeleton Loaders */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.animate-shimmer {
  animation: shimmer 2s ease-in-out infinite;
}

/* Call Ringing Animations */
@keyframes callPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(27, 132, 255, 0.7);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(27, 132, 255, 0);
  }
}

@keyframes callBounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

@keyframes callRipple {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(2.4);
    opacity: 0;
  }
}

.animate-call-pulse {
  animation: callPulse 2s ease-in-out infinite;
}

.animate-call-bounce {
  animation: callBounce 1s ease-in-out infinite;
}

.animate-call-ripple {
  animation: callRipple 1.5s ease-out infinite;
}

/* Custom colors for dark theme time tracking app */
:root {
  --custom-background: #0F111A;
  --custom-card: #1A1D2B;
  --custom-sidebar: #12141F;
  --custom-border: #2A2D3C;
  --custom-text: #FFFFFF;
  --custom-muted: #7D8597;
  --custom-body: #C0C4CC;
  --custom-primary: #1B84FF;
}

body {
  @apply bg-custom-background text-custom-body;
  /* Prevent text selection on UI elements */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Allow text selection for specific elements that should be selectable */
.selectable-text,
input,
textarea,
[contenteditable="true"],
.prose,
.content-text {
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}

/* Prevent selection on interactive elements */
button,
.button,
[role="button"],
.cursor-pointer,
.clickable {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Utility classes for text selection control */
.select-none {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.select-text {
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}

.select-all {
  -webkit-user-select: all;
  -moz-user-select: all;
  -ms-user-select: all;
  user-select: all;
}

.select-auto {
  -webkit-user-select: auto;
  -moz-user-select: auto;
  -ms-user-select: auto;
  user-select: auto;
}







.bg-custom-background { background-color: var(--custom-background); }
.bg-custom-card { background-color: var(--custom-card); }
.bg-custom-sidebar { background-color: var(--custom-sidebar); }
.bg-custom-primary { background-color: var(--custom-primary); }
.border-custom-border { border-color: var(--custom-border); }
.border-custom-primary { border-color: var(--custom-primary); }
.text-custom-text { color: var(--custom-text); }
.text-custom-muted { color: var(--custom-muted); }
.text-custom-body { color: var(--custom-body); }
.text-custom-primary { color: var(--custom-primary); }

.active-link {
  @apply bg-primary/10 text-primary border-l-2 border-primary;
}

.mini-sidebar-item {
  @apply relative w-12 h-12 flex items-center justify-center rounded-lg cursor-pointer transition-colors;
}

.mini-sidebar-item.active {
  @apply bg-primary/10;
}

.mini-sidebar-item.active::before {
  content: "";
  @apply absolute left-0 top-0 bottom-0 w-0.5 bg-primary rounded-full;
}

.timer-display {
  font-variant-numeric: tabular-nums;
  letter-spacing: 0.5px;
}

/* Custom scrollbar styling */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #2A2D3C transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.custom-scrollbar {
  scroll-behavior: smooth;
  padding-right: 4px;
}

/* Hide scrollbar by default, show on hover/scroll */
.custom-scrollbar::-webkit-scrollbar-thumb {
  visibility: hidden;
}

.custom-scrollbar:hover::-webkit-scrollbar-thumb,
.custom-scrollbar:focus::-webkit-scrollbar-thumb,
.custom-scrollbar:active::-webkit-scrollbar-thumb {
  visibility: visible;
}

/* Add custom scrollbar styles */
.overflow-y-auto, 
.overflow-auto,
main {
  scrollbar-width: thin;
  scrollbar-color: #2A2D3C transparent;
  padding-right: 12px;
}

/* For WebKit browsers (Chrome, Safari) */
.overflow-y-auto::-webkit-scrollbar,
.overflow-auto::-webkit-scrollbar,
main::-webkit-scrollbar {
  width: 4px;
}

.overflow-y-auto::-webkit-scrollbar-thumb,
.overflow-auto::-webkit-scrollbar-thumb,
main::-webkit-scrollbar-thumb {
  background-color: #2A2D3C;
  border-radius: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track,
.overflow-auto::-webkit-scrollbar-track,
main::-webkit-scrollbar-track {
  background: transparent;
}

/* Auto-hide until scroll or hover */
.overflow-y-auto::-webkit-scrollbar,
.overflow-auto::-webkit-scrollbar,
main::-webkit-scrollbar {
  opacity: 0;
  transition: opacity 0.3s;
}

.overflow-y-auto:hover::-webkit-scrollbar,
.overflow-auto:hover::-webkit-scrollbar,
main:hover::-webkit-scrollbar {
  opacity: 1;
}

/* Add responsive chat layout styles */
.chat-main-content {
  transition: all 0.3s ease;
}

/* On medium and larger screens leave space for mini-sidebar (68px) + expanded sidebar (320px) */
/* Removed margin-left to fix TeamSidebar width issue in different sections */

.chat-main-content .max-w-7xl {
  width: 100%;
  max-width: none;
}

/* Make chat view responsive when employee list is closed */
@media (min-width: 768px) {
  .chat-main-content .grid-cols-12 > .md\:col-span-7 {
    transition: all 0.3s ease;
  }
  
  .chat-main-content .grid-cols-12 > .md\:col-span-5 {
    transition: all 0.3s ease;
  }
}

/* Animation for cards */
@keyframes card-hover {
  0% { transform: translateY(0); }
  100% { transform: translateY(-3px); }
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

@media (max-width: 768px) {
  .kanban-mobile-view {
    overflow-x: auto;
    scroll-snap-type: x mandatory;
    scroll-padding: 1rem;
  }
  
  .kanban-mobile-view > div {
    scroll-snap-align: start;
    flex: 0 0 85%;
  }
}

/* Add styles to make drag and drop work better */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #2A2D3C transparent;
}

/* Add these styles to improve drag and drop */
.is-dragging {
  cursor: grabbing !important;
  pointer-events: auto !important;
  position: relative;
  z-index: 9999;
}

/* This ensures dragged items remain visible */
[data-rbd-drag-handle-context-id] {
  cursor: grab;
}

[data-rbd-drag-handle-context-id]:active {
  cursor: grabbing;
}

/* Make sure the droppable area is easily visible */
[data-rbd-droppable-id] {
  min-height: 50px; /* Ensure droppable areas have sufficient height */
}