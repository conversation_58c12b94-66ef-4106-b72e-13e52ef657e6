# تست Global Shortcuts

## تسک‌های پیاده‌سازی شده:

### تسک اول: بررسی Authentication Flow ✅
- وقتی اپ شروع می‌شود، `AuthProvider` در `useEffect` خود `checkAuth()` را فراخوانی می‌کند
- `checkAuth` از طریق Tauri به backend می‌رود و `get_auth_token` را چک می‌کند
- اگر توکن نباشد، کاربر به صفحه لاگین هدایت می‌شود
- در `logout` تابع، `clear_auth_token` فراخوانی می‌شود که تمام داده‌های auth را پاک می‌کند

### تسک دوم: Shortcut Ctrl+X ✅
- **عملکرد**: پاک کردن تمام داده‌های کاربر از store و reload کردن اپ
- **پیاده‌سازی**: 
  - Hook `useGlobalShortcuts` در `src/hooks/useGlobalShortcuts.ts`
  - تابع `clear_all_user_data` در `src-tauri/src/modules/auth.rs`
  - پاک می‌کند: `auth.json`, `user.json`, `centrifugo.json`, `error_logs.json`
  - سپس `reload_window` را فراخوانی می‌کند

### تسک سوم: Shortcut Ctrl+R ✅
- **عملکرد**: Reload کردن پنجره‌ها در Tauri
- **پیاده‌سازی**: 
  - Hook `useGlobalShortcuts` در `src/hooks/useGlobalShortcuts.ts`
  - تابع `reload_window` در `src-tauri/src/lib.rs`
  - در development mode: comprehensive window reload
  - در production mode: full app restart

## نحوه تست:

1. اپلیکیشن را اجرا کنید: `npm run tauri:dev`
2. وارد اپلیکیشن شوید (login کنید)
3. تست Ctrl+R: باید پنجره reload شود
4. تست Ctrl+X: باید تمام داده‌ها پاک شده و به صفحه login برگردد

## فایل‌های تغییر یافته:

1. `src-tauri/src/modules/auth.rs` - اضافه شدن `clear_all_user_data`
2. `src-tauri/src/lib.rs` - اضافه شدن `clear_all_user_data` به invoke_handler
3. `src/hooks/useGlobalShortcuts.ts` - Hook جدید برای global shortcuts
4. `src/App.tsx` - استفاده از `useGlobalShortcuts` hook
5. `package.json` - اضافه شدن `@tauri-apps/plugin-global-shortcut`

## نکات فنی:

- Global shortcuts در Tauri v2 باید از frontend (React) ثبت شوند
- استفاده از `@tauri-apps/plugin-global-shortcut` package
- Cleanup function برای unregister کردن shortcuts هنگام unmount
- Error handling برای هر دو shortcut