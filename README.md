# TeamBy Desktop

Professional team collaboration platform built with Tauri v2, React, TypeScript, and Tailwind CSS.

## 🚀 Quick Start

### Development
```bash
# Standardized development startup (recommended)
npm run start

# Alternative
npm run start:dev
```

### Production Build
```bash
# Standardized production build
npm run build:prod
```

## 📋 Prerequisites

- **Node.js**: v18+
- **Rust**: v1.77+
- **Tauri CLI**: v2.0+

## 🔧 WebKit Optimization

This project includes comprehensive WebKit error resolution and optimization. See [WebKit Optimization Guide](docs/WEBKIT_OPTIMIZATION_GUIDE.md) for detailed information about:

- WebKit error resolution (`internallyFailedLoadTimerFired` fixes)
- Standardized execution workflows
- Performance optimizations
- Troubleshooting guides

## 🛠️ Technology Stack

- **Frontend**: React 19.1.0 + TypeScript + Tailwind CSS
- **Desktop Framework**: Tauri v2
- **Build Tool**: Vite 7.0.2
- **UI Components**: HeroUI + Lucide Icons
- **State Management**: Zustand + Tauri Store

## 📁 Project Structure

```
teamby-desktop/
├── src/                    # React frontend source
├── src-tauri/             # Tauri backend (Rust)
├── public/                # Static assets
├── scripts/               # Standardized execution scripts
├── docs/                  # Documentation
├── dist/                  # Frontend build output
└── src-tauri/target/      # Rust build output
```

## 🔨 Available Scripts

| Command | Description |
|---------|-------------|
| `npm run start` | Start development server (recommended) |
| `npm run start:dev` | Alternative development start |
| `npm run build:prod` | Production build with optimizations |
| `npm run dev` | Vite development server only |
| `npm run build` | Frontend build only |
| `npm run tauri:dev` | Tauri development (manual) |
| `npm run tauri:build` | Tauri build (manual) |
| `npm run clean` | Clean build artifacts |
| `npm run clean:all` | Clean all artifacts including node_modules |

## 🐛 Troubleshooting

### WebKit Errors
If you encounter WebKit errors during development:
1. Use the standardized scripts: `npm run start`
2. Check the [WebKit Optimization Guide](docs/WEBKIT_OPTIMIZATION_GUIDE.md)
3. Run cleanup: `npm run clean`

### Common Issues
- **Port conflicts**: Scripts automatically handle port selection
- **Process conflicts**: Automatic cleanup included in start scripts
- **Build failures**: Use `npm run clean:all` then reinstall dependencies

## 📚 Documentation

- [WebKit Optimization Guide](docs/WEBKIT_OPTIMIZATION_GUIDE.md) - Comprehensive WebKit error resolution
- [Development Workflow](docs/DEVELOPMENT.md) - Development best practices
- [Deployment Guide](docs/DEPLOYMENT.md) - Production deployment instructions

## 🤝 Contributing

1. Use the standardized development workflow
2. Follow the WebKit optimization guidelines
3. Test on multiple platforms before submitting PRs
4. Update documentation for significant changes

## 📄 License

This project is licensed under the MIT License.
