use serde::{Deserialize, Serialize};

// API Version constant
pub const VERSION: i32 = 2;

// User Profile Types
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct IncompleteActivity {
    pub activity_id: i32,
    pub duration: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct UserProfile {
    pub full_name: String,
    pub email: String,
    pub avatar: Option<String>,
    pub position: Option<String>,
    pub company: Option<String>,
    pub is_admin: bool,
    pub screen_active: bool,
    pub incomplete_activity: Option<IncompleteActivity>,
}

// Employee Types for employees list
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Employee {
    pub id: i32,
    pub full_name: String,
    pub position_name: Option<String>,
    pub isonline: bool,
    pub screan_active: bool,
    pub avatar: String,
}

// Employee Detail Types for profile modal
#[derive(Debug, Serialize, Deserialize, <PERSON>lone)]
pub struct EmployeeContacts {
    pub email: String,
    pub phone: String,
    #[serde(default)]
    pub github: Option<String>,
    #[serde(default)]
    pub linkedin: Option<String>,
    #[serde(default)]
    pub telegram: Option<String>,
    #[serde(default)]
    pub instagram: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct EmployeeProject {
    pub name: String,
    pub role: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct EmployeeDetail {
    pub id: i32,
    pub full_name: String,
    pub email: String,
    pub phone_number: String,
    pub bio: Option<String>,
    pub skills: Vec<String>,
    pub typical_working_weekday_start: Option<String>,
    pub typical_working_weekday_end: Option<String>,
    pub typical_working_weekend: Option<String>,
    pub score: f64, // Changed from i32 to f64
    pub slogan: Option<String>,
    pub projects: Vec<EmployeeProject>,
    pub position: Option<String>,
    pub created_at: String,
    pub city: Option<String>,
    pub country: Option<String>,
    pub contacts: EmployeeContacts,
    pub isonline: bool,
    pub last_seen: String,
}

// Login Response Types
#[derive(Debug, Deserialize)]
#[allow(dead_code)]
pub struct LoginResponse {
    pub success: bool,
    pub token: String,
    pub message: String,
}

// Activity Stats Types
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ActivityStat {
    pub title: String,
    pub slug: String,
    pub icon: String,
    pub value: f64,
}

#[derive(Debug, Deserialize)]
#[allow(dead_code)]
pub struct UserProfileResponse {
    pub success: bool,
    pub data: UserProfile,
    pub message: String,
}

#[derive(Debug, Deserialize)]
pub struct ServerUserProfile {
    pub full_name: String,
    pub email: String,
    pub avatar: Option<String>,
    pub position: Option<String>,
    pub company: Option<String>,
    pub is_admin: bool,
    pub screan_active: bool, // Server typo
    pub incomplete_activity: Option<IncompleteActivity>,
}

#[derive(Debug, Deserialize)]
#[allow(dead_code)]
pub struct ServerUserProfileResponse {
    pub success: bool,
    pub data: ServerUserProfile,
    pub message: String,
}

// Project Types
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Project {
    pub id: i32,
    pub name: String,
}

#[derive(Debug, Deserialize)]
#[allow(dead_code)]
pub struct ProjectsResponse {
    pub success: bool,
    pub data: Vec<Project>,
    pub message: String,
}

// Task Types
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Task {
    pub id: i32,
    pub name: String,
}

#[derive(Debug, Deserialize)]
#[allow(dead_code)]
pub struct TasksResponse {
    pub success: bool,
    pub data: Vec<Task>,
    pub message: String,
}

// Activity Types
#[derive(Debug, Serialize)]
pub struct ActivityStartRequest {
    pub project_id: Option<i32>,
    pub task_id: Option<i32>,
    pub system_local_time: String,
    pub platform: String,
    pub device_name: String,
    pub device_os: String,
}

#[derive(Debug, Deserialize)]
#[allow(dead_code)]
pub struct ActivityStartResponse {
    pub id: i32,
    pub start_time: String,
    pub last_ready: Option<i64>,
    pub end_time: Option<String>,
    pub proposed_start_time: Option<String>,
    pub proposed_end_time: Option<String>,
    pub edit_status: String,
    pub edit_reason: Option<String>,
    pub edit_log: Option<String>,
    pub price: Option<f64>,
    #[serde(rename = "type")]
    pub activity_type: Option<String>,
    pub reports_audios: Option<String>,
    pub report_text: Option<String>,
    pub report_score: Option<f64>,
    pub report_improvement_suggestion: Option<String>,
    pub jira_task_id: Option<String>,
    pub jira_task_name: Option<String>,
    pub system_local_time: String,
    pub platform: String,
    pub device_name: String,
    pub device_os: String,
    pub version: Option<String>,
    pub is_activity_submitted: Option<bool>,
    pub employee: i32,
    pub project: Option<i32>,
    pub project_goal: Option<String>,
    pub target_goal: Option<String>,
}

#[derive(Debug, Serialize)]
pub struct ActivityEndRequest {
    pub activity_id: i32,
    pub project_id: Option<i32>,
    pub task_id: Option<i32>,
    pub notes: Option<String>,
    pub duration: String,
    pub imready_error_logs_count: i32,
}

#[derive(Debug, Serialize)]
pub struct ActivityUpdateRequest {
    pub activity_id: i32,
    pub project_id: Option<i32>,
    pub task_id: Option<i32>,
    pub notes: Option<String>,
    pub imready_error_logs_count: Option<i32>,
}

// Error Log Types
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ErrorLog {
    pub id: Option<i32>,
    pub activity_id: i32,
    pub error_message: String,
    pub timestamp: String,
}

#[derive(Debug, Deserialize)]
#[allow(dead_code)]
pub struct ErrorLogsResponse {
    pub success: bool,
    pub data: Vec<ErrorLog>,
    pub message: String,
}

// System Info Types
#[derive(Debug, Serialize, Deserialize)]
pub struct SystemInfo {
    pub platform: String,
    pub device_name: String,
    pub device_os: String,
    pub local_time: String,
}

// Centrifugo Types
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct CentrifugoToken {
    pub token: String,
    pub user_id: i32,
    pub ws_url: String,
    pub expires_in: i64,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct CentrifugoTokenResponse {
    pub token: String,
    pub user_id: i32,
    pub ws_url: String,
    pub expires_in: i64,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct CentrifugoMessage {
    pub message_type: String,
    pub timestamp: i64,
    pub employee_id: i32,
    pub employee_name: String,
    pub status: String,
    #[serde(default)]
    pub screen_active: Option<bool>, // Optional field for employee_update_screen_view messages
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub enum CentrifugoConnectionState {
    Disconnected,
    Connecting,
    Connected,
    Reconnecting,
    Error(String),
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct CentrifugoStatus {
    pub state: CentrifugoConnectionState,
    pub user_id: Option<i32>,
    pub channel: Option<String>,
    pub last_message_time: Option<i64>,
}
