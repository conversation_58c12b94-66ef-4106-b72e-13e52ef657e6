use tauri::AppHandle;
use tauri_plugin_store::StoreBuilder;
use std::time::Duration;

use super::utils::{get_auth_token, create_http_client, log_api_request, log_api_response};
use super::types::LoginResponse;

// Clear authentication token
#[tauri::command]
pub async fn clear_auth_token(app: AppHandle) -> Result<bool, String> {
    println!("🗑️ [AUTH] Clearing authentication token...");
    
    let store = match StoreBuilder::new(&app, "auth.json").build() {
        Ok(store) => store,
        Err(e) => return Err(format!("Failed to build store: {}", e)),
    };

    let deleted = store.delete("token");
    if deleted {
        match store.save() {
            Ok(_) => {
                println!("✅ [AUTH] Authentication token cleared successfully");
                Ok(true)
            }
            Err(e) => {
                let error_msg = format!("Failed to save store after clearing token: {}", e);
                println!("❌ [AUTH] {}", error_msg);
                Err(error_msg)
            }
        }
    } else {
        println!("⚠️ [AUTH] Token not found or already cleared");
        Ok(true)
    }
}

// Check if user is authenticated
#[tauri::command]
pub async fn is_authenticated(app: AppHandle) -> Result<bool, String> {
    match get_auth_token(&app).await {
        Ok(_) => Ok(true),
        Err(_) => Ok(false),
    }
}

// Get stored authentication token
#[tauri::command]
pub async fn get_stored_token(app: AppHandle) -> Result<String, String> {
    get_auth_token(&app).await
}

// Check authentication status
#[tauri::command]
pub async fn check_auth(app: AppHandle) -> Result<serde_json::Value, String> {
    println!("🔐 [AUTH] Checking authentication status...");

    match get_auth_token(&app).await {
        Ok(token) => {
            println!("✅ [AUTH] User is authenticated");
            Ok(serde_json::json!({
                "success": true,
                "token": token,
                "message": "User is authenticated"
            }))
        }
        Err(_) => {
            println!("❌ [AUTH] User is not authenticated");
            Ok(serde_json::json!({
                "success": false,
                "message": "User is not authenticated"
            }))
        }
    }
}

// Login command
#[tauri::command]
pub async fn login(app: AppHandle, email: String, password: String) -> Result<serde_json::Value, String> {
    println!("🔐 [AUTH] Attempting login for email: {}", email);

    // Create HTTP client
    let client = create_http_client();

    // Prepare request body
    let request_body = serde_json::json!({
        "email": email,
        "password": password
    });

    let url = "http://127.0.0.1:8000/api/v2/employees/signin/";
    log_api_request("POST", url, "LOGIN");

    // Make API request
    let response = match client
        .post(url)
        .header("Content-Type", "application/json")
        .json(&request_body)
        .timeout(Duration::from_secs(30))
        .send()
        .await
    {
        Ok(response) => response,
        Err(e) => {
            let error_msg = format!("Network error: {}", e);
            println!("❌ [AUTH] {}", error_msg);
            return Err(error_msg);
        }
    };

    let status = response.status();
    log_api_response(&status, "LOGIN");

    match status.as_u16() {
        200 => {
            // Parse response
            match response.json::<LoginResponse>().await {
                Ok(login_response) => {
                    println!("✅ [AUTH] Login successful with token: {}", &login_response.token[..8]);

                    // Store the actual token from server response
                    let store = match StoreBuilder::new(&app, "auth.json").build() {
                        Ok(store) => store,
                        Err(e) => return Err(format!("Failed to build store: {}", e)),
                    };

                    store.set("token", serde_json::Value::String(login_response.token.clone()));
                    if let Err(e) = store.save() {
                        return Err(format!("Failed to save token: {}", e));
                    }

                    Ok(serde_json::json!({
                        "success": true,
                        "token": login_response.token,
                        "message": login_response.message
                    }))
                }
                Err(e) => {
                    let error_msg = format!("Failed to parse login response: {}", e);
                    println!("❌ [AUTH] {}", error_msg);
                    Err(error_msg)
                }
            }
        }
        400 => {
            println!("❌ [AUTH] Invalid credentials");
            Ok(serde_json::json!({
                "success": false,
                "message": "Invalid email or password"
            }))
        }
        401 => {
            println!("❌ [AUTH] Unauthorized");
            Ok(serde_json::json!({
                "success": false,
                "message": "Invalid credentials"
            }))
        }
        _ => {
            let error_msg = format!("Login failed with status: {}", status);
            println!("❌ [AUTH] {}", error_msg);
            Err(error_msg)
        }
    }
}

// Logout command
#[tauri::command]
pub async fn logout(app: AppHandle) -> Result<serde_json::Value, String> {
    println!("🔐 [AUTH] Logging out user...");

    // Clear token from store
    match clear_auth_token(app).await {
        Ok(_) => {
            println!("✅ [AUTH] Logout successful");
            Ok(serde_json::json!({
                "success": true,
                "message": "Logout successful"
            }))
        }
        Err(e) => {
            println!("❌ [AUTH] Logout failed: {}", e);
            Err(e)
        }
    }
}

// Clear all user data from all stores
#[tauri::command]
pub async fn clear_all_user_data(app: AppHandle) -> Result<bool, String> {
    println!("🗑️ [AUTH] Clearing all user data from all stores...");
    
    let store_files = vec!["auth.json", "user.json", "centrifugo.json", "error_logs.json"];
    let mut all_cleared = true;
    
    for store_file in store_files {
        match StoreBuilder::new(&app, store_file).build() {
            Ok(store) => {
                // Clear all keys from the store
                store.clear();
                match store.save() {
                    Ok(_) => {
                        println!("✅ [AUTH] Cleared store: {}", store_file);
                    }
                    Err(e) => {
                        println!("❌ [AUTH] Failed to save cleared store {}: {}", store_file, e);
                        all_cleared = false;
                    }
                }
            }
            Err(e) => {
                println!("⚠️ [AUTH] Could not access store {}: {}", store_file, e);
                // Don't mark as failed since store might not exist yet
            }
        }
    }
    
    if all_cleared {
        println!("✅ [AUTH] All user data cleared successfully");
    } else {
        println!("⚠️ [AUTH] Some stores could not be cleared completely");
    }
    
    Ok(all_cleared)
}

// Legacy alias for clear_auth_token
#[tauri::command]
pub async fn clear_token(app: AppHandle) -> Result<bool, String> {
    println!("🗑️ [AUTH] Legacy clear_token called - redirecting to clear_auth_token");
    clear_auth_token(app).await
}

// Make call API request
#[tauri::command]
pub async fn make_call_request(app: AppHandle, target_employee_id: u32) -> Result<serde_json::Value, String> {
    println!("📞 [CALL] Making call request to employee ID: {}", target_employee_id);

    // Get authentication token
    let token = match get_auth_token(&app).await {
        Ok(token) => token,
        Err(_) => {
            println!("❌ [CALL] Authentication token not found");
            return Err("Authentication required".to_string());
        }
    };

    // Create HTTP client
    let client = create_http_client();

    // Prepare request body
    let request_body = serde_json::json!({
        "target_employee_id": target_employee_id,
        "meet_type": "audio"
    });

    let url = "http://127.0.0.1:8000/api/meet/call/";
    log_api_request("POST", url, "CALL");

    // Make API request
    let response = match client
        .post(url)
        .header("Content-Type", "application/json")
        .header("Authorization", format!("Bearer {}", token))
        .json(&request_body)
        .timeout(Duration::from_secs(30))
        .send()
        .await
    {
        Ok(response) => response,
        Err(e) => {
            let error_msg = format!("Network error: {}", e);
            println!("❌ [CALL] {}", error_msg);
            return Err(error_msg);
        }
    };

    let status = response.status();
    log_api_response(&status, "CALL");

    match status.as_u16() {
        201 => {
            // Parse response
            match response.json::<serde_json::Value>().await {
                Ok(call_response) => {
                    println!("✅ [CALL] Call request successful");
                    Ok(serde_json::json!({
                        "success": true,
                        "status": 201,
                        "data": call_response
                    }))
                }
                Err(e) => {
                    let error_msg = format!("Failed to parse call response: {}", e);
                    println!("❌ [CALL] {}", error_msg);
                    Err(error_msg)
                }
            }
        }
        _ => {
            let error_msg = format!("Call request failed with status: {}", status);
            println!("❌ [CALL] {}", error_msg);
            Ok(serde_json::json!({
                "success": false,
                "status": status.as_u16(),
                "message": error_msg
            }))
        }
    }
}


