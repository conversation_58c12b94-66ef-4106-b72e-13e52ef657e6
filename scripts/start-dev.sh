#!/bin/bash

# TeamBy Desktop - Standardized Development Startup Script
# This script ensures proper startup sequence and WebKit error mitigation

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="TeamBy Desktop"
REQUIRED_NODE_VERSION="18"
REQUIRED_RUST_VERSION="1.77"

echo -e "${BLUE}🚀 Starting ${PROJECT_NAME} Development Environment${NC}"
echo -e "${BLUE}================================================${NC}"

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check Node.js version
check_node_version() {
    if command_exists node; then
        NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
        if [ "$NODE_VERSION" -ge "$REQUIRED_NODE_VERSION" ]; then
            echo -e "${GREEN}✅ Node.js version: $(node --version)${NC}"
            return 0
        else
            echo -e "${RED}❌ Node.js version $(node --version) is too old. Required: v${REQUIRED_NODE_VERSION}+${NC}"
            return 1
        fi
    else
        echo -e "${RED}❌ Node.js not found${NC}"
        return 1
    fi
}

# Function to check Rust version
check_rust_version() {
    if command_exists rustc; then
        RUST_VERSION=$(rustc --version | cut -d' ' -f2 | cut -d'.' -f1-2)
        echo -e "${GREEN}✅ Rust version: $(rustc --version)${NC}"
        return 0
    else
        echo -e "${RED}❌ Rust not found${NC}"
        return 1
    fi
}

# Function to check Tauri CLI
check_tauri_cli() {
    if command_exists cargo && cargo tauri --version >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Tauri CLI: $(cargo tauri --version)${NC}"
        return 0
    else
        echo -e "${YELLOW}⚠️ Tauri CLI not found, installing...${NC}"
        cargo install tauri-cli --version "^2.0"
        return $?
    fi
}

# Function to clean up previous processes
cleanup_processes() {
    echo -e "${YELLOW}🧹 Cleaning up previous processes...${NC}"
    
    # Kill any existing Tauri processes
    pkill -f "tauri dev" 2>/dev/null || true
    pkill -f "cargo run" 2>/dev/null || true
    pkill -f "target/debug/app" 2>/dev/null || true
    
    # Kill Vite dev server and related Node processes
    pkill -f "vite" 2>/dev/null || true
    pkill -f "node.*vite" 2>/dev/null || true
    
    # Kill WebKit processes to prevent errors
    pkill -f "webkit" 2>/dev/null || true
    pkill -f "WebKitWebProcess" 2>/dev/null || true
    pkill -f "WebKitNetworkProcess" 2>/dev/null || true
    pkill -f "WebKitGPUProcess" 2>/dev/null || true
    
    # Kill any remaining TeamBy processes
    pkill -f "TeamBy" 2>/dev/null || true
    pkill -f "desktop" 2>/dev/null || true
    
    # Clean up any stuck ports
    if command_exists lsof; then
        echo -e "${YELLOW}🔍 Checking for stuck ports...${NC}"
        lsof -ti:3000 | xargs kill -9 2>/dev/null || true
        lsof -ti:1421 | xargs kill -9 2>/dev/null || true
    fi
    
    # Wait for processes to terminate
    sleep 3
    
    echo -e "${GREEN}✅ Cleanup completed${NC}"
}

# Function to install dependencies
install_dependencies() {
    echo -e "${YELLOW}📦 Installing dependencies...${NC}"

    # Install Node.js dependencies with compatibility handling
    if [ -f "package.json" ]; then
        # Try normal install first
        if npm install 2>/dev/null; then
            echo -e "${GREEN}✅ Node.js dependencies installed${NC}"
        else
            echo -e "${YELLOW}⚠️ Standard install failed, trying with legacy peer deps...${NC}"
            npm install --legacy-peer-deps
            echo -e "${GREEN}✅ Node.js dependencies installed (with legacy peer deps)${NC}"
        fi
    fi

    # Build Rust dependencies
    if [ -f "src-tauri/Cargo.toml" ]; then
        cd src-tauri
        cargo build
        cd ..
        echo -e "${GREEN}✅ Rust dependencies built${NC}"
    fi
}

# Function to optimize system for WebKit
optimize_webkit() {
    echo -e "${YELLOW}🔧 Optimizing system for WebKit...${NC}"
    
    # Set environment variables to reduce WebKit errors
    export WEBKIT_DISABLE_COMPOSITING_MODE=1
    export WEBKIT_DISABLE_DMABUF_RENDERER=1
    export WEBKIT_FORCE_SANDBOX=0
    export WEBKIT_DISABLE_TBS=1
    export WEBKIT_DISABLE_HARDWARE_ACCELERATION=1
    export WEBKIT_DISABLE_GPU_SANDBOX=1
    
    # Additional WebKit stability improvements
    export WEBKIT_DISABLE_MEDIA_STREAM=0
    export WEBKIT_DISABLE_WEB_SECURITY=0
    export WEBKIT_DISABLE_FEATURES="VizDisplayCompositor"
    
    # Linux-specific optimizations
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Set display variables
        export DISPLAY=${DISPLAY:-:0}
        
        # Additional Linux WebKit fixes
        export WEBKIT_DISABLE_SHARED_MEMORY=1
        export WEBKIT_DISABLE_DEV_SHM_USAGE=1
        export WEBKIT_NO_SANDBOX=1
        
        # Force software rendering if hardware acceleration causes issues
        export LIBGL_ALWAYS_SOFTWARE=1
        export GALLIUM_DRIVER=llvmpipe
        
        echo -e "${GREEN}✅ Linux WebKit optimizations applied${NC}"
    fi
    
    # macOS-specific optimizations
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS WebKit optimizations
        export WEBKIT_DISABLE_METAL=1
        export WEBKIT_DISABLE_CORE_ANIMATION=1
        echo -e "${GREEN}✅ macOS WebKit optimizations applied${NC}"
    fi
}

# Function to start development server
start_dev_server() {
    echo -e "${YELLOW}🔥 Starting development server...${NC}"
    
    # Set Tauri-specific environment variables
    export TAURI_DEV_WATCHER_IGNORE_FILE="target"
    export RUST_BACKTRACE=1
    export RUST_LOG=warn
    
    # Start Tauri development server with optimized settings
    echo -e "${BLUE}📱 Launching Tauri application...${NC}"
    echo -e "${YELLOW}⚠️ Note: WebKit warnings are normal and will be suppressed${NC}"
    
    # Use npm script which handles the proper sequence
    npm run tauri:dev
}

# Main execution
main() {
    # Check prerequisites
    echo -e "${BLUE}🔍 Checking prerequisites...${NC}"
    
    if ! check_node_version; then
        echo -e "${RED}❌ Please install Node.js ${REQUIRED_NODE_VERSION}+ and try again${NC}"
        exit 1
    fi
    
    if ! check_rust_version; then
        echo -e "${RED}❌ Please install Rust ${REQUIRED_RUST_VERSION}+ and try again${NC}"
        exit 1
    fi
    
    if ! check_tauri_cli; then
        echo -e "${RED}❌ Failed to install Tauri CLI${NC}"
        exit 1
    fi
    
    # Cleanup and prepare
    cleanup_processes
    optimize_webkit
    install_dependencies
    
    # Start the application
    echo -e "${GREEN}🎉 All checks passed! Starting ${PROJECT_NAME}...${NC}"
    echo -e "${BLUE}================================================${NC}"
    
    start_dev_server
}

# Handle script interruption
trap 'echo -e "\n${YELLOW}🛑 Development server stopped${NC}"; cleanup_processes; exit 0' INT TERM

# Run main function
main "$@"
