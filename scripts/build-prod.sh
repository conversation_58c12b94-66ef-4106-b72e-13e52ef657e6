#!/bin/bash

# TeamBy Desktop - Standardized Production Build Script
# This script ensures proper build sequence and optimization

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="TeamBy Desktop"
BUILD_DIR="src-tauri/target/release"
DIST_DIR="dist"

echo -e "${BLUE}🏗️ Building ${PROJECT_NAME} for Production${NC}"
echo -e "${BLUE}============================================${NC}"

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to clean previous builds
clean_build() {
    echo -e "${YELLOW}🧹 Cleaning previous builds...${NC}"
    
    # Clean Rust build artifacts
    if [ -d "src-tauri/target" ]; then
        rm -rf src-tauri/target/release
        echo -e "${GREEN}✅ Cleaned Rust build artifacts${NC}"
    fi
    
    # Clean frontend build artifacts
    if [ -d "$DIST_DIR" ]; then
        rm -rf "$DIST_DIR"
        echo -e "${GREEN}✅ Cleaned frontend build artifacts${NC}"
    fi
    
    # Clean node_modules/.vite cache
    if [ -d "node_modules/.vite" ]; then
        rm -rf node_modules/.vite
        echo -e "${GREEN}✅ Cleaned Vite cache${NC}"
    fi
}

# Function to install dependencies
install_dependencies() {
    echo -e "${YELLOW}📦 Installing production dependencies...${NC}"
    
    # Install Node.js dependencies
    npm ci --production=false
    echo -e "${GREEN}✅ Node.js dependencies installed${NC}"
}

# Function to run tests
run_tests() {
    echo -e "${YELLOW}🧪 Running tests...${NC}"
    
    # Run TypeScript type checking
    if command_exists tsc; then
        npm run build  # This includes TypeScript compilation
        echo -e "${GREEN}✅ TypeScript compilation successful${NC}"
    fi
    
    # Run linting
    if [ -f "eslint.config.js" ]; then
        npm run lint
        echo -e "${GREEN}✅ Linting passed${NC}"
    fi
    
    # Run unit tests if available
    if grep -q "\"test\"" package.json; then
        npm run test:run 2>/dev/null || echo -e "${YELLOW}⚠️ No tests found or tests skipped${NC}"
    fi
}

# Function to optimize for production
optimize_production() {
    echo -e "${YELLOW}⚡ Optimizing for production...${NC}"
    
    # Set production environment variables
    export NODE_ENV=production
    export TAURI_ENV=production
    export VITE_ENV=production
    
    # Optimize Rust build
    export CARGO_PROFILE_RELEASE_LTO=true
    export CARGO_PROFILE_RELEASE_CODEGEN_UNITS=1
    export CARGO_PROFILE_RELEASE_PANIC=abort
    
    # WebKit optimizations for production
    export WEBKIT_DISABLE_COMPOSITING_MODE=1
    export WEBKIT_DISABLE_DMABUF_RENDERER=1
    
    echo -e "${GREEN}✅ Production optimizations applied${NC}"
}

# Function to build frontend
build_frontend() {
    echo -e "${YELLOW}🎨 Building frontend...${NC}"
    
    # Build with Vite
    npm run build
    
    if [ -d "$DIST_DIR" ]; then
        echo -e "${GREEN}✅ Frontend build completed${NC}"
        
        # Show build size
        DIST_SIZE=$(du -sh "$DIST_DIR" | cut -f1)
        echo -e "${BLUE}📊 Frontend build size: ${DIST_SIZE}${NC}"
    else
        echo -e "${RED}❌ Frontend build failed${NC}"
        exit 1
    fi
}

# Function to build Tauri application
build_tauri() {
    echo -e "${YELLOW}🦀 Building Tauri application...${NC}"
    
    # Build Tauri application
    npm run tauri:build
    
    if [ -d "$BUILD_DIR" ]; then
        echo -e "${GREEN}✅ Tauri build completed${NC}"
        
        # Find and display built executables
        echo -e "${BLUE}📦 Built executables:${NC}"
        find "$BUILD_DIR" -name "app" -type f -executable 2>/dev/null | while read -r file; do
            SIZE=$(du -sh "$file" | cut -f1)
            echo -e "${GREEN}  • $file (${SIZE})${NC}"
        done
        
        # Find bundle files
        BUNDLE_DIR="src-tauri/target/release/bundle"
        if [ -d "$BUNDLE_DIR" ]; then
            echo -e "${BLUE}📦 Built bundles:${NC}"
            find "$BUNDLE_DIR" -type f \( -name "*.deb" -o -name "*.rpm" -o -name "*.AppImage" -o -name "*.dmg" -o -name "*.msi" \) 2>/dev/null | while read -r file; do
                SIZE=$(du -sh "$file" | cut -f1)
                echo -e "${GREEN}  • $(basename "$file") (${SIZE})${NC}"
            done
        fi
    else
        echo -e "${RED}❌ Tauri build failed${NC}"
        exit 1
    fi
}

# Function to verify build
verify_build() {
    echo -e "${YELLOW}🔍 Verifying build...${NC}"
    
    # Check if executable exists and is functional
    EXECUTABLE="$BUILD_DIR/app"
    if [ -f "$EXECUTABLE" ]; then
        # Test if executable can run (basic check)
        if "$EXECUTABLE" --version >/dev/null 2>&1 || "$EXECUTABLE" --help >/dev/null 2>&1; then
            echo -e "${GREEN}✅ Executable verification passed${NC}"
        else
            echo -e "${YELLOW}⚠️ Executable exists but may have runtime issues${NC}"
        fi
    else
        echo -e "${RED}❌ Executable not found${NC}"
        exit 1
    fi
    
    # Verify bundle integrity
    BUNDLE_DIR="src-tauri/target/release/bundle"
    if [ -d "$BUNDLE_DIR" ]; then
        BUNDLE_COUNT=$(find "$BUNDLE_DIR" -type f \( -name "*.deb" -o -name "*.rpm" -o -name "*.AppImage" -o -name "*.dmg" -o -name "*.msi" \) 2>/dev/null | wc -l)
        if [ "$BUNDLE_COUNT" -gt 0 ]; then
            echo -e "${GREEN}✅ Found $BUNDLE_COUNT bundle(s)${NC}"
        else
            echo -e "${YELLOW}⚠️ No bundles found${NC}"
        fi
    fi
}

# Function to create release summary
create_release_summary() {
    echo -e "${BLUE}📋 Build Summary${NC}"
    echo -e "${BLUE}===============${NC}"
    
    # Build information
    echo -e "${GREEN}Project: ${PROJECT_NAME}${NC}"
    echo -e "${GREEN}Build Date: $(date)${NC}"
    echo -e "${GREEN}Node.js: $(node --version)${NC}"
    echo -e "${GREEN}Rust: $(rustc --version)${NC}"
    
    # File sizes
    if [ -d "$DIST_DIR" ]; then
        FRONTEND_SIZE=$(du -sh "$DIST_DIR" | cut -f1)
        echo -e "${GREEN}Frontend Size: ${FRONTEND_SIZE}${NC}"
    fi
    
    if [ -f "$BUILD_DIR/app" ]; then
        EXECUTABLE_SIZE=$(du -sh "$BUILD_DIR/app" | cut -f1)
        echo -e "${GREEN}Executable Size: ${EXECUTABLE_SIZE}${NC}"
    fi
    
    # Bundle information
    BUNDLE_DIR="src-tauri/target/release/bundle"
    if [ -d "$BUNDLE_DIR" ]; then
        echo -e "${GREEN}Bundles:${NC}"
        find "$BUNDLE_DIR" -type f \( -name "*.deb" -o -name "*.rpm" -o -name "*.AppImage" -o -name "*.dmg" -o -name "*.msi" \) 2>/dev/null | while read -r file; do
            SIZE=$(du -sh "$file" | cut -f1)
            echo -e "${GREEN}  • $(basename "$file"): ${SIZE}${NC}"
        done
    fi
}

# Main execution
main() {
    # Check prerequisites
    if ! command_exists node; then
        echo -e "${RED}❌ Node.js not found${NC}"
        exit 1
    fi
    
    if ! command_exists rustc; then
        echo -e "${RED}❌ Rust not found${NC}"
        exit 1
    fi
    
    if ! command_exists cargo || ! cargo tauri --version >/dev/null 2>&1; then
        echo -e "${RED}❌ Tauri CLI not found${NC}"
        exit 1
    fi
    
    # Build process
    clean_build
    install_dependencies
    optimize_production
    run_tests
    build_frontend
    build_tauri
    verify_build
    create_release_summary
    
    echo -e "${GREEN}🎉 Production build completed successfully!${NC}"
    echo -e "${BLUE}================================================${NC}"
}

# Handle script interruption
trap 'echo -e "\n${YELLOW}🛑 Build process interrupted${NC}"; exit 1' INT TERM

# Run main function
main "$@"
