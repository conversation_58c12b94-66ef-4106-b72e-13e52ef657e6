---
description: Repository Information Overview
alwaysApply: true
---

# TeamBy Desktop Information

## Summary
TeamBy Desktop is a professional team collaboration platform built with Tauri v2, React, TypeScript, and Tailwind CSS. It provides a comprehensive desktop application for team communication, file sharing, meetings, and project management.

## Structure
- **src/**: React frontend source code with TypeScript
- **src-tauri/**: Tauri backend written in Rust
- **public/**: Static assets and HTML templates
- **scripts/**: Standardized execution scripts for development and production
- **docs/**: Documentation files including WebKit optimization guides
- **tests/**: Test files for components and functionality

## Language & Runtime
**Frontend Language**: TypeScript
**Backend Language**: Rust (v1.77.2)
**Runtime**: Node.js (v18+)
**Build System**: Vite + Tauri
**Package Manager**: npm

## Dependencies
**Main Dependencies**:
- React (v19.1.0)
- React DOM (v19.1.0)
- React Router DOM (v7.6.3)
- Tauri API (v2.6.0)
- LiveKit Client (v2.15.2)
- Framer Motion (v12.23.0)
- Date-fns (v4.1.0)
- DND Kit (v6.3.1)

**Development Dependencies**:
- TypeScript (v5.8.3)
- Vite (v7.0.0)
- Vitest (v3.2.4)
- Tailwind CSS (v3.4.17)
- ESLint (v9.29.0)
- Testing Library (React, DOM, Jest DOM)

## Build & Installation
```bash
# Development
npm run start

# Production Build
npm run build:prod

# Manual Commands
npm run tauri:dev    # Start Tauri development server
npm run tauri:build  # Build Tauri application
```

## Tauri Configuration
**Windows**: 
- Main application window (1200x800)
- Screen view window (900x600)
- Splash screen (350x350)
- Call window (400x350)

**Plugins**:
- clipboard-manager
- dialog
- fs
- notification
- store
- log
- global-shortcut
- os
- shell
- system-info

## Testing
**Framework**: Vitest with Testing Library
**Test Location**: src/test/ and src/tests/
**Configuration**: vitest.config.ts
**Run Command**:
```bash
npm run test       # Run tests in watch mode
npm run test:run   # Run tests once
npm run test:ui    # Run tests with UI
```

## WebKit Optimization
The project includes comprehensive WebKit error resolution and optimization, with environment variables set in the development and production scripts to mitigate common WebKit issues:
```bash
WEBKIT_DISABLE_COMPOSITING_MODE=1
WEBKIT_DISABLE_DMABUF_RENDERER=1
WEBKIT_FORCE_SANDBOX=0
WEBKIT_DISABLE_TBS=1
```