#!/bin/bash

# TeamBy Desktop - WebKit Test Script
# This script tests WebKit functionality and diagnoses issues

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔍 TeamBy Desktop - WebKit Diagnostic Test${NC}"
echo -e "${BLUE}===========================================${NC}"

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Test 1: Check system WebKit
echo -e "${YELLOW}🔍 Testing system WebKit...${NC}"
if command_exists webkit2gtk-4.0-dev; then
    echo -e "${GREEN}✅ WebKit2GTK development package found${NC}"
else
    echo -e "${RED}❌ WebKit2GTK development package not found${NC}"
    echo -e "${YELLOW}💡 Install with: sudo apt install webkit2gtk-4.0-dev${NC}"
fi

# Test 2: Check Tauri dependencies
echo -e "${YELLOW}🔍 Testing Tauri dependencies...${NC}"
if command_exists pkg-config; then
    echo -e "${GREEN}✅ pkg-config found${NC}"
else
    echo -e "${RED}❌ pkg-config not found${NC}"
fi

# Test 3: Check network connectivity
echo -e "${YELLOW}🔍 Testing network connectivity...${NC}"
if curl -s --max-time 5 http://localhost:3000 > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Vite dev server is accessible${NC}"
else
    echo -e "${RED}❌ Cannot reach Vite dev server at localhost:3000${NC}"
    echo -e "${YELLOW}💡 Make sure Vite is running: npm run dev${NC}"
fi

# Test 4: Check for conflicting processes
echo -e "${YELLOW}🔍 Checking for conflicting processes...${NC}"
webkit_processes=$(ps aux | grep -E "(webkit|WebKit)" | grep -v grep | wc -l)
if [ $webkit_processes -gt 0 ]; then
    echo -e "${YELLOW}⚠️ Found $webkit_processes WebKit processes running${NC}"
    ps aux | grep -E "(webkit|WebKit)" | grep -v grep
else
    echo -e "${GREEN}✅ No conflicting WebKit processes found${NC}"
fi

# Test 5: Check environment variables
echo -e "${YELLOW}🔍 Checking WebKit environment variables...${NC}"
env_vars=(
    "WEBKIT_DISABLE_COMPOSITING_MODE"
    "WEBKIT_DISABLE_DMABUF_RENDERER"
    "WEBKIT_FORCE_SANDBOX"
    "WEBKIT_DISABLE_TBS"
)

for var in "${env_vars[@]}"; do
    if [ -n "${!var}" ]; then
        echo -e "${GREEN}✅ $var=${!var}${NC}"
    else
        echo -e "${YELLOW}⚠️ $var not set${NC}"
    fi
done

# Test 6: Check display
echo -e "${YELLOW}🔍 Checking display configuration...${NC}"
if [ -n "$DISPLAY" ]; then
    echo -e "${GREEN}✅ DISPLAY=$DISPLAY${NC}"
else
    echo -e "${RED}❌ DISPLAY not set${NC}"
fi

# Test 7: Memory and resources
echo -e "${YELLOW}🔍 Checking system resources...${NC}"
free_mem=$(free -m | awk 'NR==2{printf "%.1f%%", $3*100/$2 }')
echo -e "${BLUE}📊 Memory usage: $free_mem${NC}"

# Test 8: Check for WebKit libraries
echo -e "${YELLOW}🔍 Checking WebKit libraries...${NC}"
if ldconfig -p | grep -q webkit2gtk; then
    echo -e "${GREEN}✅ WebKit2GTK libraries found${NC}"
else
    echo -e "${RED}❌ WebKit2GTK libraries not found${NC}"
fi

echo -e "${BLUE}===========================================${NC}"
echo -e "${GREEN}🎯 Diagnostic complete!${NC}"
echo -e "${YELLOW}💡 If issues persist, run: ./cleanup.sh && npm run start${NC}"