#!/bin/bash

echo "🧹 FORCE CLEANUP - Killing all Tauri development processes..."

# Step 1: Kill all npm processes first
echo "🔪 Killing all npm processes..."
pkill -9 -f "npm" 2>/dev/null || true

# Step 2: Kill specific Node.js processes (avoid VS Code extensions)
echo "🔪 Killing specific Node.js processes..."
pkill -9 -f "node.*vite" 2>/dev/null || true
pkill -9 -f "node.*tauri" 2>/dev/null || true
pkill -9 -f "node.*desktop" 2>/dev/null || true
pkill -9 -f "node.*3000" 2>/dev/null || true
pkill -9 -f "node.*1420" 2>/dev/null || true
pkill -9 -f "node.*5173" 2>/dev/null || true

# Step 3: Kill all Tauri related processes with extreme force
echo "🔪 Force killing all Tauri processes..."
pkill -9 -f "tauri" 2>/dev/null || true
pkill -9 -f "cargo-tauri" 2>/dev/null || true
pkill -9 -f "target/debug/app" 2>/dev/null || true
pkill -9 -f "vite" 2>/dev/null || true

# Step 4: Kill WebKit processes (prevent WebKit errors)
echo "🔪 Killing WebKit processes..."
pkill -9 -f "webkit" 2>/dev/null || true
pkill -9 -f "WebKitWebProcess" 2>/dev/null || true
pkill -9 -f "WebKitNetworkProcess" 2>/dev/null || true



# Step 5: Kill all cargo processes
echo "🔪 Killing all cargo processes..."
pkill -9 -f "cargo" 2>/dev/null || true

# Step 6: Kill all processes containing "desktop" in the path
echo "🔪 Killing desktop related processes..."
pkill -9 -f "desktop" 2>/dev/null || true

# Step 7: Clear WebKit cache and temporary files
echo "🧹 Clearing WebKit cache and temporary files..."
rm -rf ~/.cache/webkit* 2>/dev/null || true
rm -rf ~/.cache/tauri* 2>/dev/null || true
rm -rf /tmp/tauri* 2>/dev/null || true
rm -rf /tmp/webkit* 2>/dev/null || true
rm -rf ~/.local/share/tauri* 2>/dev/null || true

# Step 8: Kill any sh processes that might be stuck
echo "🔪 Killing stuck shell processes..."
pkill -9 -f "sh -c" 2>/dev/null || true

# Step 9: Kill any zombie processes
echo "🔪 Clearing zombie processes..."
pkill -9 -f "defunct" 2>/dev/null || true

# Step 10: Force kill processes holding common ports
echo "🔪 Force killing processes on ports..."
fuser -k 3000/tcp 2>/dev/null || true
fuser -k 1420/tcp 2>/dev/null || true
fuser -k 8080/tcp 2>/dev/null || true
fuser -k 5173/tcp 2>/dev/null || true

# Step 11: Wait for processes to die
echo "⏳ Waiting for processes to terminate..."
sleep 3

# Step 12: Nuclear option - kill by process tree
echo "💥 Nuclear cleanup..."
# Kill any remaining processes that might be related (avoid VS Code extensions)
ps aux | grep -E "(tauri|vite|cargo|npm|webkit)" | grep -v grep | awk '{print $2}' | xargs -r kill -9 2>/dev/null || true
ps aux | grep -E "node.*(vite|tauri|desktop|3000|1420|5173)" | grep -v grep | awk '{print $2}' | xargs -r kill -9 2>/dev/null || true

# Step 13: Clear any remaining zombie processes
echo "🧟 Final zombie cleanup..."
ps aux | grep -E "(defunct|zombie)" | grep -v grep | awk '{print $2}' | xargs -r kill -9 2>/dev/null || true

# Step 14: Final verification
echo "🔍 Final verification..."
remaining=$(ps aux | grep -E "(tauri|vite|cargo|target/debug/app|webkit)" | grep -v grep | wc -l)

if [ $remaining -eq 0 ]; then
    echo "✅ TOTAL CLEANUP SUCCESSFUL! All processes eliminated!"
else
    echo "⚠️  Some stubborn processes detected:"
    ps aux | grep -E "(tauri|vite|cargo|target/debug/app|webkit)" | grep -v grep
    echo "🔥 Attempting final extermination..."
    ps aux | grep -E "(tauri|vite|cargo|target/debug/app|webkit)" | grep -v grep | awk '{print $2}' | xargs -r kill -9 2>/dev/null || true
fi

# Step 15: Reset network settings (prevent WebKit network errors)
echo "🌐 Resetting network settings..."
sudo systemctl restart NetworkManager 2>/dev/null || true
sudo systemctl flush-dns 2>/dev/null || true
sudo systemd-resolve --flush-caches 2>/dev/null || true

echo "🚀 System is now clean and ready for fresh start!"
echo "💡 You can now run: npm run start (recommended) or npm run tauri:dev"
echo "🔧 For best results, use: ./scripts/start-dev.sh"